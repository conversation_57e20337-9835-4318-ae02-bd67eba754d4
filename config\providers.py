"""
AI Provider Preset Management for BlendPro v2.1.0
Unified OpenAI-compatible provider preset system for multi-provider support
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import json
import os

class AuthenticationType(Enum):
    """Authentication type enumeration"""
    API_KEY = "api_key"
    BEARER_TOKEN = "bearer"
    OAUTH = "oauth"
    CUSTOM_HEADER = "custom_header"

@dataclass
class RateLimit:
    """Rate limiting configuration"""
    requests_per_minute: int = 60
    tokens_per_minute: int = 100000
    concurrent_requests: int = 10

@dataclass
class CostConfig:
    """Cost configuration for provider"""
    input_cost_per_1k_tokens: float = 0.0
    output_cost_per_1k_tokens: float = 0.0
    image_cost_per_request: float = 0.0
    currency: str = "USD"

@dataclass
class ProviderPreset:
    """OpenAI-compatible provider preset configuration"""
    name: str
    display_name: str
    api_endpoint: str
    api_key: str = ""

    # Authentication
    authentication_type: AuthenticationType = AuthenticationType.API_KEY
    custom_headers: Dict[str, str] = field(default_factory=dict)

    # Capabilities
    supported_models: List[str] = field(default_factory=list)
    capabilities: List[str] = field(default_factory=list)
    supports_vision: bool = False
    supports_function_calling: bool = False
    supports_streaming: bool = True

    # Rate limiting and costs
    rate_limits: RateLimit = field(default_factory=RateLimit)
    cost_config: CostConfig = field(default_factory=CostConfig)

    # Configuration
    default_model: str = ""
    timeout: float = 30.0
    max_retries: int = 3

    # Status
    is_active: bool = True
    is_custom: bool = False  # True for user-created presets
    last_tested: Optional[str] = None
    test_status: str = "unknown"  # unknown, success, failed

    # Metadata for custom presets
    description: str = ""
    setup_instructions: str = ""  # For presets requiring special setup (e.g., LiteLLM)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "name": self.name,
            "display_name": self.display_name,
            "api_endpoint": self.api_endpoint,
            "api_key": self.api_key,
            "authentication_type": self.authentication_type.value,
            "custom_headers": self.custom_headers,
            "supported_models": self.supported_models,
            "capabilities": self.capabilities,
            "supports_vision": self.supports_vision,
            "supports_function_calling": self.supports_function_calling,
            "supports_streaming": self.supports_streaming,
            "rate_limits": {
                "requests_per_minute": self.rate_limits.requests_per_minute,
                "tokens_per_minute": self.rate_limits.tokens_per_minute,
                "concurrent_requests": self.rate_limits.concurrent_requests
            },
            "cost_config": {
                "input_cost_per_1k_tokens": self.cost_config.input_cost_per_1k_tokens,
                "output_cost_per_1k_tokens": self.cost_config.output_cost_per_1k_tokens,
                "image_cost_per_request": self.cost_config.image_cost_per_request,
                "currency": self.cost_config.currency
            },
            "default_model": self.default_model,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "is_active": self.is_active,
            "is_custom": self.is_custom,
            "last_tested": self.last_tested,
            "test_status": self.test_status,
            "description": self.description,
            "setup_instructions": self.setup_instructions
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProviderPreset':
        """Create from dictionary"""
        rate_limits_data = data.get("rate_limits", {})
        cost_config_data = data.get("cost_config", {})

        return cls(
            name=data["name"],
            display_name=data["display_name"],
            api_endpoint=data["api_endpoint"],
            api_key=data.get("api_key", ""),
            authentication_type=AuthenticationType(data.get("authentication_type", "api_key")),
            custom_headers=data.get("custom_headers", {}),
            supported_models=data.get("supported_models", []),
            capabilities=data.get("capabilities", []),
            supports_vision=data.get("supports_vision", False),
            supports_function_calling=data.get("supports_function_calling", False),
            supports_streaming=data.get("supports_streaming", True),
            rate_limits=RateLimit(
                requests_per_minute=rate_limits_data.get("requests_per_minute", 60),
                tokens_per_minute=rate_limits_data.get("tokens_per_minute", 100000),
                concurrent_requests=rate_limits_data.get("concurrent_requests", 10)
            ),
            cost_config=CostConfig(
                input_cost_per_1k_tokens=cost_config_data.get("input_cost_per_1k_tokens", 0.0),
                output_cost_per_1k_tokens=cost_config_data.get("output_cost_per_1k_tokens", 0.0),
                image_cost_per_request=cost_config_data.get("image_cost_per_request", 0.0),
                currency=cost_config_data.get("currency", "USD")
            ),
            default_model=data.get("default_model", ""),
            timeout=data.get("timeout", 30.0),
            max_retries=data.get("max_retries", 3),
            is_active=data.get("is_active", True),
            is_custom=data.get("is_custom", False),
            last_tested=data.get("last_tested"),
            test_status=data.get("test_status", "unknown"),
            description=data.get("description", ""),
            setup_instructions=data.get("setup_instructions", "")
        )

# Predefined provider preset configurations
DEFAULT_PROVIDER_PRESETS: Dict[str, ProviderPreset] = {
    "openai": ProviderPreset(
        name="openai",
        display_name="OpenAI",
        api_endpoint="https://api.openai.com/v1",
        supported_models=["gpt-4", "gpt-4-turbo", "gpt-4o", "gpt-4o-mini", "gpt-3.5-turbo"],
        capabilities=["text_generation", "vision", "code_generation", "function_calling"],
        supports_vision=True,
        supports_function_calling=True,
        default_model="gpt-4o-mini",
        cost_config=CostConfig(
            input_cost_per_1k_tokens=0.00015,
            output_cost_per_1k_tokens=0.0006
        ),
        description="Official OpenAI API endpoint"
    ),

    "anthropic_litellm": ProviderPreset(
        name="anthropic_litellm",
        display_name="Anthropic (via LiteLLM)",
        api_endpoint="http://localhost:4000/v1",
        supported_models=["claude-3-5-sonnet-20241022", "claude-3-opus-20240229", "claude-3-haiku-20240307"],
        capabilities=["text_generation", "vision", "code_generation", "long_context"],
        supports_vision=True,
        supports_function_calling=True,
        default_model="claude-3-5-sonnet-20241022",
        cost_config=CostConfig(
            input_cost_per_1k_tokens=0.003,
            output_cost_per_1k_tokens=0.015
        ),
        description="Anthropic Claude models via LiteLLM proxy",
        setup_instructions="Requires LiteLLM proxy server running on localhost:4000. Install with: pip install litellm && litellm --model anthropic/claude-3-5-sonnet-20241022"
    ),

    "openrouter": ProviderPreset(
        name="openrouter",
        display_name="OpenRouter",
        api_endpoint="https://openrouter.ai/api/v1",
        supported_models=["mistral/mistral-large", "anthropic/claude-3.5-sonnet", "openai/gpt-4o"],
        capabilities=["text_generation", "vision", "code_generation"],
        supports_vision=True,
        supports_function_calling=True,
        default_model="mistral/mistral-large",
        cost_config=CostConfig(
            input_cost_per_1k_tokens=0.002,
            output_cost_per_1k_tokens=0.006
        ),
        description="OpenRouter aggregated AI models"
    )
}

def get_default_provider_presets() -> Dict[str, ProviderPreset]:
    """Get default provider preset configurations"""
    return DEFAULT_PROVIDER_PRESETS.copy()

def validate_provider_preset_config(preset: ProviderPreset) -> List[str]:
    """Validate provider preset configuration and return list of errors"""
    errors = []

    if not preset.name:
        errors.append("Preset name is required")

    if not preset.display_name:
        errors.append("Preset display name is required")

    if not preset.api_endpoint:
        errors.append("API endpoint is required")

    # Validate endpoint format
    if not preset.api_endpoint.startswith(('http://', 'https://')):
        errors.append("API endpoint must start with http:// or https://")

    if preset.authentication_type == AuthenticationType.API_KEY and not preset.api_key:
        errors.append("API key is required for API key authentication")

    if not preset.supported_models:
        errors.append("At least one supported model must be specified")

    if preset.default_model and preset.default_model not in preset.supported_models:
        errors.append("Default model must be in supported models list")

    return errors

def get_provider_preset_by_name(name: str) -> Optional[ProviderPreset]:
    """Get provider preset by name from defaults"""
    return DEFAULT_PROVIDER_PRESETS.get(name)

def get_all_provider_preset_names() -> List[str]:
    """Get all available provider preset names"""
    return list(DEFAULT_PROVIDER_PRESETS.keys())

def get_models_for_provider_preset(preset_name: str) -> List[str]:
    """Get supported models for a provider preset"""
    preset = get_provider_preset_by_name(preset_name)
    return preset.supported_models if preset else []

# Backward compatibility aliases (deprecated)
def get_default_providers() -> Dict[str, ProviderPreset]:
    """DEPRECATED: Use get_default_provider_presets() instead"""
    return get_default_provider_presets()

def validate_provider_config(provider: ProviderPreset) -> List[str]:
    """DEPRECATED: Use validate_provider_preset_config() instead"""
    return validate_provider_preset_config(provider)
