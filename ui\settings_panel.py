"""
Settings Panel for BlendPro: AI Co-Pilot
Addon preferences and configuration interface
"""

import bpy
from bpy.types import AddonPreferences
from bpy.props import StringProperty, BoolProperty, FloatProperty, IntProperty, EnumProperty

from ..config.settings import get_settings
from ..config.models import get_model_choices, get_vision_model_choices
from ..utils.api_client import get_api_client
from .style_guide import (
    BlendProLayout, BlendProIcons, BlendProSpacing,
    BlendProValidation, get_health_status_text
)

class BLENDPROAddonPreferences(AddonPreferences):
    """BlendPro addon preferences"""
    bl_idname = __package__.split('.')[0]  # Get main package name
    
    # API Configuration
    api_key = StringProperty(
        name="OpenAI API Key",
        description="Your OpenAI API key for AI functionality",
        default="",
        subtype='PASSWORD'
    )

    custom_api_url = StringProperty(
        name="Custom API URL",
        description="Custom API endpoint URL (leave empty for OpenAI)",
        default=""
    )

    use_custom_model = BoolProperty(
        name="Use Custom Model",
        description="Use a custom model instead of the default",
        default=False
    )

    custom_model = StringProperty(
        name="Custom Model",
        description="Custom model name (e.g., gpt-4o-mini, gpt-4o, claude-3-5-sonnet)",
        default=""  # Will be set dynamically
    )
    
    # Vision Configuration
    vision_api_key = StringProperty(
        name="Vision API Key",
        description="API key for vision-capable models (can be same as main API key)",
        default="",
        subtype='PASSWORD'
    )

    vision_api_url = StringProperty(
        name="Vision API URL",
        description="API endpoint for vision models",
        default=""
    )

    vision_model = StringProperty(
        name="Vision Model",
        description="Model to use for vision tasks",
        default=""  # Will be set dynamically
    )
    
    # AI Behavior Settings
    temperature = FloatProperty(
        name="Temperature",
        description="Controls randomness in AI responses (0.0 = deterministic, 1.0 = creative)",
        default=0.7,
        min=0.0,
        max=2.0,
        step=0.1
    )
    
    max_tokens = IntProperty(
        name="Max Tokens",
        description="Maximum tokens in AI response",
        default=1500,
        min=100,
        max=4000
    )
    
    # Feature Toggles
    enable_vision_context = BoolProperty(
        name="Enable Vision Context",
        description="Include viewport screenshots in AI analysis",
        default=True
    )
    
    enable_multi_step_planning = BoolProperty(
        name="Enable Multi-Step Planning",
        description="Break complex tasks into multiple steps",
        default=True
    )
    
    enable_proactive_suggestions = BoolProperty(
        name="Enable Proactive Suggestions",
        description="Show AI-generated suggestions based on your workflow",
        default=True
    )
    
    enable_scene_monitoring = BoolProperty(
        name="Enable Scene Monitoring",
        description="Monitor scene health and provide real-time feedback",
        default=True
    )
    
    enable_auto_backup = BoolProperty(
        name="Enable Auto Backup",
        description="Automatically backup scene before code execution",
        default=True
    )
    
    enable_caching = BoolProperty(
        name="Enable Caching",
        description="Cache API responses to improve performance",
        default=True
    )
    
    # Performance Settings
    monitoring_interval = FloatProperty(
        name="Monitoring Interval",
        description="Seconds between scene health checks",
        default=2.0,
        min=0.5,
        max=10.0
    )
    
    max_concurrent_requests = IntProperty(
        name="Max Concurrent Requests",
        description="Maximum number of simultaneous API requests",
        default=3,
        min=1,
        max=10
    )
    
    max_suggestions = IntProperty(
        name="Max Suggestions",
        description="Maximum number of active suggestions to show",
        default=5,
        min=1,
        max=20
    )
    
    backup_interval = IntProperty(
        name="Backup Interval",
        description="Minimum seconds between automatic backups",
        default=300,  # 5 minutes
        min=60,
        max=3600
    )
    
    max_backups = IntProperty(
        name="Max Backups",
        description="Maximum number of backup files to keep",
        default=10,
        min=1,
        max=50
    )
    
    analysis_cooldown = FloatProperty(
        name="Analysis Cooldown",
        description="Minimum seconds between scene analyses",
        default=1.0,
        min=0.1,
        max=10.0
    )

    # ===== v2.1.0 MULTI-AGENT SYSTEM SETTINGS =====
    enable_multi_agent_system = BoolProperty(
        name="Enable Multi-Agent System",
        description="Enable collaborative multi-agent AI system",
        default=True
    )

    default_agent_selection = EnumProperty(
        name="Default Agent Selection",
        description="How agents are selected for tasks",
        items=[
            ('auto', 'Auto', 'Automatically select best agent for task'),
            ('manual', 'Manual', 'Manually select agent for each task'),
            ('single', 'Single', 'Use single general agent')
        ],
        default='auto'
    )

    agent_confidence_threshold = FloatProperty(
        name="Agent Confidence Threshold",
        description="Minimum confidence required for agent selection",
        default=0.85,
        min=0.0,
        max=1.0,
        step=0.05
    )

    # ===== PROVIDER MANAGEMENT SETTINGS =====
    enable_provider_failover = BoolProperty(
        name="Enable Provider Failover",
        description="Automatically switch to backup provider on failure",
        default=True
    )

    provider_timeout = FloatProperty(
        name="Provider Timeout",
        description="Timeout for provider requests in seconds",
        default=30.0,
        min=5.0,
        max=120.0
    )

    default_provider_fallback = EnumProperty(
        name="Default Fallback Provider",
        description="Default provider to use when primary fails",
        items=[
            ('openai', 'OpenAI', 'OpenAI API'),
            ('anthropic', 'Anthropic', 'Anthropic Claude API'),
            ('google', 'Google', 'Google AI API'),
            ('mistral', 'Mistral', 'Mistral AI API'),
            ('openrouter', 'OpenRouter', 'OpenRouter API')
        ],
        default='openai'
    )

    # ===== COLLABORATIVE AI SETTINGS =====
    enable_collaborative_analysis = BoolProperty(
        name="Enable Collaborative Analysis",
        description="Use multiple AI models for consensus-based analysis",
        default=True
    )

    consensus_threshold = FloatProperty(
        name="Consensus Threshold",
        description="Agreement threshold for collaborative decisions",
        default=0.75,
        min=0.5,
        max=1.0,
        step=0.05
    )

    max_parallel_models = IntProperty(
        name="Max Parallel Models",
        description="Maximum number of models to run in parallel",
        default=3,
        min=1,
        max=5
    )

    # ===== ENHANCED VISION SETTINGS =====
    enable_enhanced_vision = BoolProperty(
        name="Enable Enhanced Vision",
        description="Enable advanced vision analysis capabilities",
        default=True
    )

    enable_depth_analysis = BoolProperty(
        name="Enable Depth Analysis",
        description="Include depth buffer analysis in vision processing",
        default=True
    )

    enable_spatial_reasoning = BoolProperty(
        name="Enable Spatial Reasoning",
        description="Enable spatial relationship analysis",
        default=True
    )

    enable_temporal_tracking = BoolProperty(
        name="Enable Temporal Tracking",
        description="Track changes over time in scene analysis",
        default=True
    )

    vision_analysis_quality = EnumProperty(
        name="Vision Analysis Quality",
        description="Quality level for vision analysis",
        items=[
            ('fast', 'Fast', 'Quick analysis with basic insights'),
            ('balanced', 'Balanced', 'Standard quality with good performance'),
            ('detailed', 'Detailed', 'High quality analysis with full features')
        ],
        default='balanced'
    )

    # ===== COST MANAGEMENT SETTINGS =====
    enable_cost_tracking = BoolProperty(
        name="Enable Cost Tracking",
        description="Track and monitor API usage costs",
        default=True
    )

    monthly_cost_limit = FloatProperty(
        name="Monthly Cost Limit (USD)",
        description="Maximum monthly spending limit",
        default=100.0,
        min=1.0,
        max=1000.0
    )

    cost_alert_threshold = FloatProperty(
        name="Cost Alert Threshold (%)",
        description="Alert when reaching this percentage of monthly limit",
        default=80.0,
        min=50.0,
        max=95.0
    )

    # ===== PERFORMANCE MONITORING SETTINGS =====
    monitor_providers = BoolProperty(
        name="Monitor Providers",
        description="Monitor AI provider performance",
        default=True
    )

    monitor_agents = BoolProperty(
        name="Monitor Agents",
        description="Monitor agent performance",
        default=True
    )

    monitor_vision = BoolProperty(
        name="Monitor Vision",
        description="Monitor vision system performance",
        default=True
    )

    monitor_memory = BoolProperty(
        name="Monitor Memory",
        description="Monitor memory usage",
        default=False
    )

    auto_refresh_performance = BoolProperty(
        name="Auto Refresh Performance",
        description="Automatically refresh performance data",
        default=False
    )

    # ===== AGENT CONFIGURATION SETTINGS =====
    default_agent_type = EnumProperty(
        name="Default Agent Type",
        description="Default agent type for new tasks",
        items=[
            ('vision_specialist', 'Vision Specialist', 'Specialized in scene analysis and visual understanding'),
            ('code_architect', 'Code Architect', 'Specialized in code generation and architecture'),
            ('scene_optimizer', 'Scene Optimizer', 'Specialized in performance and workflow optimization'),
            ('ux_advisor', 'UX Advisor', 'Specialized in user experience improvements'),
            ('performance_analyst', 'Performance Analyst', 'Specialized in system performance analysis'),
            ('general_assistant', 'General Assistant', 'General purpose assistant')
        ],
        default='vision_specialist'
    )

    agent_max_tokens = IntProperty(
        name="Agent Max Tokens",
        description="Maximum tokens for agent responses",
        default=2000,
        min=100,
        max=8000
    )

    agent_temperature = FloatProperty(
        name="Agent Temperature",
        description="Temperature setting for agent responses",
        default=0.7,
        min=0.0,
        max=2.0,
        step=0.1
    )

    agent_top_p = FloatProperty(
        name="Agent Top P",
        description="Top P setting for agent responses",
        default=1.0,
        min=0.0,
        max=1.0,
        step=0.05
    )
    
    def draw(self, context):
        """Draw improved preferences interface with tabbed layout"""
        layout = self.layout

        # Create tab selector
        self._draw_tab_selector(layout, context)

        # Get active tab from scene property (default to 'BASIC')
        active_tab = getattr(context.scene, 'blendpro_settings_tab', 'BASIC')

        # Draw content based on active tab
        if active_tab == 'BASIC':
            self._draw_basic_settings(layout)
        elif active_tab == 'AGENTS':
            self._draw_agent_settings(layout)
        elif active_tab == 'VISION':
            self._draw_vision_settings_tab(layout)
        elif active_tab == 'PERFORMANCE':
            self._draw_performance_settings_tab(layout)
        elif active_tab == 'ADVANCED':
            self._draw_advanced_settings(layout)

    def _draw_tab_selector(self, layout, context):
        """Draw improved tab selector with health indicator"""
        # Main tab container
        tab_box = layout.box()

        # Header with system health
        header = tab_box.row()
        header.label(text="BlendPro Settings", icon='PREFERENCES')

        # System health indicator
        health_score = BlendProValidation.get_system_health_score(self)
        health_text = get_health_status_text(health_score)

        health_row = header.row()
        health_row.alignment = 'RIGHT'
        health_row.scale_y = 0.8

        if health_score >= 80:
            health_row.label(text=f"System: {health_text} ({health_score}%)", icon=BlendProIcons.CONNECTED)
        elif health_score >= 60:
            health_row.label(text=f"System: {health_text} ({health_score}%)", icon=BlendProIcons.WARNING)
        else:
            health_row.label(text=f"System: {health_text} ({health_score}%)", icon=BlendProIcons.DISCONNECTED)

        tab_box.separator(factor=BlendProSpacing.SUBSECTION)

        # Tab buttons with improved styling
        tab_row = tab_box.row(align=True)
        tab_row.scale_y = BlendProSpacing.TAB_SCALE

        # Get current active tab
        active_tab = getattr(context.scene, 'blendpro_settings_tab', 'BASIC')

        # Tab definitions with icons from style guide
        tabs = [
            ('BASIC', 'Basic', BlendProIcons.BASIC),
            ('AGENTS', 'Agents', BlendProIcons.AGENTS),
            ('VISION', 'Vision', BlendProIcons.VISION),
            ('PERFORMANCE', 'Performance', BlendProIcons.PERFORMANCE),
            ('ADVANCED', 'Advanced', BlendProIcons.ADVANCED)
        ]

        for tab_id, tab_name, icon in tabs:
            # Create button for each tab
            if active_tab == tab_id:
                # Active tab - use different styling
                btn = tab_row.operator("blendpro.set_settings_tab", text=f"● {tab_name}", icon=icon)
            else:
                # Inactive tab
                btn = tab_row.operator("blendpro.set_settings_tab", text=tab_name, icon=icon)

            btn.tab = tab_id

        layout.separator(factor=BlendProSpacing.SECTION)

    def _draw_basic_settings(self, layout):
        """Draw basic settings tab"""
        # API Configuration
        self._draw_api_configuration(layout)

        # AI Behavior
        self._draw_ai_behavior(layout)

        # Essential Features
        box = layout.box()
        box.label(text="Essential Features", icon='MODIFIER')

        col = box.column()
        col.prop(self, "enable_vision_context")
        col.prop(self, "enable_multi_step_planning")
        col.prop(self, "enable_proactive_suggestions")
        col.prop(self, "enable_auto_backup")

    def _draw_agent_settings(self, layout):
        """Draw agent settings tab"""
        # Multi-Agent System
        self._draw_multi_agent_settings(layout)

        # Agent Configuration
        self._draw_agent_configuration(layout)

        # Provider Management
        self._draw_provider_settings(layout)

    def _draw_vision_settings_tab(self, layout):
        """Draw vision settings tab"""
        self._draw_vision_settings(layout)

    def _draw_performance_settings_tab(self, layout):
        """Draw performance settings tab"""
        self._draw_performance_settings(layout)

        # Cost Management
        self._draw_cost_management(layout)

    def _draw_advanced_settings(self, layout):
        """Draw advanced settings tab"""
        # All remaining feature toggles
        self._draw_feature_toggles(layout)

        # System Status
        self._draw_system_status(layout)

        # Advanced options
        box = layout.box()
        box.label(text="Advanced Options", icon='TOOL_SETTINGS')

        col = box.column()
        col.prop(self, "enable_caching")
        col.prop(self, "enable_scene_monitoring")

        # Reset settings button
        box.separator()
        row = box.row()
        row.operator("blendpro.reset_settings", text="Reset All Settings", icon='FILE_REFRESH')

    def _draw_api_configuration(self, layout):
        """Draw API configuration section"""
        box = layout.box()
        box.label(text="API Configuration", icon='WORLD')
        
        # Main API settings
        main_box = box.box()
        main_box.label(text="Main API", icon='PLUGIN')
        
        main_box.prop(self, "api_key")
        main_box.prop(self, "custom_api_url")
        
        # Model selection
        model_row = main_box.row()
        model_row.prop(self, "use_custom_model")
        
        if self.use_custom_model:
            model_row.prop(self, "custom_model", text="")
        
        # Vision API settings
        vision_box = box.box()
        vision_box.label(text="Vision API", icon='CAMERA_DATA')
        
        vision_box.prop(self, "vision_api_key")
        vision_box.prop(self, "vision_api_url")
        vision_box.prop(self, "vision_model")
        
        # Action buttons
        action_row = box.row(align=True)
        action_row.scale_y = 1.2
        action_row.operator("blendpro.initialize_ai", text="Initialize AI", icon='PLAY')
        action_row.operator("blendpro.test_api_connection", text="Test Connection", icon='LINKED')
    
    def _draw_ai_behavior(self, layout):
        """Draw AI behavior settings with improved layout"""
        box = layout.box()

        # Header with description
        header = box.row()
        header.label(text="AI Behavior", icon='SETTINGS')

        box.separator(factor=0.3)

        # Create a grid for better organization
        grid = box.grid_flow(columns=2, align=True)

        # Temperature setting with visual indicator
        temp_col = grid.column()
        temp_box = temp_col.box()
        temp_box.label(text="Response Style", icon='TEMP')
        temp_box.prop(self, "temperature", text="Temperature")

        # Add visual indicator for temperature
        temp_row = temp_box.row()
        temp_row.scale_y = 0.7
        if self.temperature < 0.3:
            temp_row.label(text="Conservative", icon='FREEZE')
        elif self.temperature > 0.8:
            temp_row.label(text="Creative", icon='FORCE_HARMONIC')
        else:
            temp_row.label(text="Balanced", icon='CHECKMARK')

        # Token limit setting
        token_col = grid.column()
        token_box = token_col.box()
        token_box.label(text="Response Length", icon='TEXT')
        token_box.prop(self, "max_tokens", text="Max Tokens")

        # Add usage indicator
        usage_row = token_box.row()
        usage_row.scale_y = 0.7
        if self.max_tokens < 1000:
            usage_row.label(text="Short responses", icon='SMALL_CAPS')
        elif self.max_tokens > 2000:
            usage_row.label(text="Detailed responses", icon='LONGDISPLAY')
        else:
            usage_row.label(text="Standard responses", icon='ALIGN_JUSTIFY')
    
    def _draw_feature_toggles(self, layout):
        """Draw feature toggle settings with improved organization"""
        box = layout.box()

        # Header with feature count
        header = box.row()
        header.label(text="Feature Configuration", icon='MODIFIER')

        # Count enabled features
        enabled_count = sum([
            self.enable_vision_context,
            self.enable_multi_step_planning,
            self.enable_proactive_suggestions,
            self.enable_scene_monitoring,
            self.enable_auto_backup,
            self.enable_caching
        ])

        header.label(text=f"{enabled_count}/6")

        box.separator(factor=0.3)

        # Create two columns for better organization
        grid = box.grid_flow(columns=2, align=True)

        # Left column - AI Features
        left_col = grid.column()
        ai_box = left_col.box()
        ai_box.label(text="AI Features", icon='OUTLINER_OB_LIGHT')

        ai_col = ai_box.column(align=True)
        ai_col.prop(self, "enable_vision_context", text="Vision Context")
        ai_col.prop(self, "enable_multi_step_planning", text="Multi-Step Planning")
        ai_col.prop(self, "enable_proactive_suggestions", text="Proactive Suggestions")

        # Right column - System Features
        right_col = grid.column()
        system_box = right_col.box()
        system_box.label(text="System Features", icon='SYSTEM')

        system_col = system_box.column(align=True)
        system_col.prop(self, "enable_scene_monitoring", text="Scene Monitoring")
        system_col.prop(self, "enable_auto_backup", text="Auto Backup")
        system_col.prop(self, "enable_caching", text="Response Caching")
    
    def _draw_performance_settings(self, layout):
        """Draw performance settings"""
        box = layout.box()
        box.label(text="Performance", icon='PREFERENCES')

        # Performance monitoring
        monitor_box = box.box()
        monitor_box.label(text="Performance Monitoring", icon='VIEWZOOM')

        col = monitor_box.column()
        col.prop(self, "monitor_providers")
        col.prop(self, "monitor_agents")
        col.prop(self, "monitor_vision")
        col.prop(self, "monitor_memory")
        col.prop(self, "auto_refresh_performance")

        # Scene monitoring settings
        scene_box = box.box()
        scene_box.label(text="Scene Monitoring", icon='SCENE_DATA')

        row = scene_box.row()
        row.prop(self, "monitoring_interval")
        row.prop(self, "analysis_cooldown")

        # Request settings
        request_box = box.box()
        request_box.label(text="Requests", icon='INTERNET')

        row = request_box.row()
        row.prop(self, "max_concurrent_requests")
        row.prop(self, "max_suggestions")

        # Backup settings
        backup_box = box.box()
        backup_box.label(text="Backups", icon='FILE_BACKUP')

        row = backup_box.row()
        row.prop(self, "backup_interval")
        row.prop(self, "max_backups")
    
    def _draw_system_status(self, layout):
        """Draw system status information"""
        box = layout.box()
        box.label(text="System Status", icon='SYSTEM')
        
        # API status
        api_client = get_api_client()
        
        status_row = box.row()
        if self.api_key:
            status_row.label(text="API: Configured", icon='CHECKMARK')
        else:
            status_row.label(text="API: Not Configured", icon='ERROR')
        
        # Create status grid for better organization
        grid = box.grid_flow(columns=2, align=True)

        # Left column - API Status
        left_col = grid.column()
        api_status_box = left_col.box()
        api_status_box.label(text="API Status", icon='WORLD')

        api_col = api_status_box.column(align=True)

        # Main API
        main_row = api_col.row()
        if self.api_key:
            main_row.label(text="Main API", icon='CHECKMARK')
            main_row.label(text="Ready")
        else:
            main_row.label(text="Main API", icon='ERROR')
            main_row.label(text="Not Set")

        # Vision API
        vision_row = api_col.row()
        if self.vision_api_key:
            vision_row.label(text="Vision API", icon='CHECKMARK')
            vision_row.label(text="Ready")
        else:
            vision_row.label(text="Vision API", icon='ERROR')
            vision_row.label(text="Not Set")

        # Right column - System Stats
        right_col = grid.column()
        stats_box = right_col.box()
        stats_box.label(text="System Stats", icon='INFO')

        stats_col = stats_box.column(align=True)

        # Cache stats
        try:
            cache_stats = api_client.get_cache_stats()
            cache_count = cache_stats.get('cached_requests', 0)
        except:
            cache_count = 0

        cache_row = stats_col.row()
        cache_row.label(text="Cache", icon='DISK_DRIVE')
        cache_row.label(text=f"{cache_count} items")

        # Feature status
        feature_row = stats_col.row()
        feature_row.label(text="Features", icon='MODIFIER')

        enabled_features = sum([
            self.enable_vision_context,
            self.enable_multi_step_planning,
            self.enable_proactive_suggestions,
            self.enable_scene_monitoring,
            self.enable_auto_backup,
            self.enable_caching
        ])

        feature_row.label(text=f"{enabled_features}/6 active")

        # System actions
        box.separator()
        actions_box = box.box()
        actions_box.label(text="System Actions", icon='TOOL_SETTINGS')

        actions_row = actions_box.row(align=True)
        actions_row.scale_y = 1.1
        actions_row.operator("blendpro.clear_cache", text="Clear Cache", icon='TRASH')
        actions_row.operator("blendpro.test_api_connection", text="Test APIs", icon='LINKED')

    def _draw_multi_agent_settings(self, layout):
        """Draw multi-agent system settings"""
        box = layout.box()
        box.label(text="Multi-Agent System", icon='GROUP')

        col = box.column()
        col.prop(self, "enable_multi_agent_system")

        if self.enable_multi_agent_system:
            col.prop(self, "default_agent_selection")
            col.prop(self, "agent_confidence_threshold")
            col.prop(self, "enable_collaborative_analysis")

            if self.enable_collaborative_analysis:
                row = col.row()
                row.prop(self, "consensus_threshold")
                row.prop(self, "max_parallel_models")

    def _draw_provider_settings(self, layout):
        """Draw provider management settings"""
        box = layout.box()
        box.label(text="Provider Management", icon='NETWORK_DRIVE')

        col = box.column()
        col.prop(self, "enable_provider_failover")

        if self.enable_provider_failover:
            col.prop(self, "default_provider_fallback")

        col.prop(self, "provider_timeout")

        # Provider Presets Management
        self._draw_provider_presets_management(layout)

    def _draw_vision_settings(self, layout):
        """Draw enhanced vision settings"""
        box = layout.box()
        box.label(text="Enhanced Vision", icon='CAMERA_DATA')

        col = box.column()
        col.prop(self, "enable_enhanced_vision")

        if self.enable_enhanced_vision:
            col.prop(self, "enable_depth_analysis")
            col.prop(self, "enable_spatial_reasoning")
            col.prop(self, "enable_temporal_tracking")
            col.prop(self, "vision_analysis_quality")

    def _draw_provider_presets_management(self, layout):
        """Draw provider presets management UI"""
        box = layout.box()
        box.label(text="Provider Presets", icon='PRESET')

        # Get provider manager
        from ..utils.provider_manager import get_provider_manager
        provider_manager = get_provider_manager()

        # Show existing presets
        presets_box = box.box()
        presets_box.label(text="Available Presets", icon='OUTLINER_DATA_LIGHTPROBE')

        all_presets = provider_manager.get_all_provider_presets()
        custom_presets = provider_manager.get_custom_presets()

        # Built-in presets
        if all_presets:
            builtin_box = presets_box.box()
            builtin_box.label(text="Built-in Presets", icon='PACKAGE')

            for name, preset in all_presets.items():
                if not preset.is_custom:
                    row = builtin_box.row()
                    row.label(text=f"{preset.display_name} ({name})", icon='NETWORK_DRIVE')
                    status_icon = 'CHECKMARK' if preset.is_active else 'X'
                    row.label(text="", icon=status_icon)

        # Custom presets
        if custom_presets:
            custom_box = presets_box.box()
            custom_box.label(text="Custom Presets", icon='MODIFIER_DATA')

            for name, preset in custom_presets.items():
                row = custom_box.row()
                row.label(text=f"{preset.display_name} ({name})", icon='SETTINGS')
                status_icon = 'CHECKMARK' if preset.is_active else 'X'
                row.label(text="", icon=status_icon)

                # Edit and delete buttons for custom presets
                edit_row = custom_box.row()
                edit_row.operator("blendpro.edit_custom_preset", text="Edit").preset_name = name
                edit_row.operator("blendpro.delete_custom_preset", text="Delete").preset_name = name

        # Add new preset button
        add_row = box.row()
        add_row.operator("blendpro.add_custom_preset", text="Add Custom Preset", icon='ADD')

        # LiteLLM setup info
        info_box = box.box()
        info_box.label(text="Setup Information", icon='INFO')
        info_col = info_box.column()
        info_col.label(text="For Anthropic models, install LiteLLM proxy:")
        info_col.label(text="pip install litellm")
        info_col.label(text="litellm --model anthropic/claude-3-5-sonnet-20241022")

    def _draw_agent_configuration(self, layout):
        """Draw agent configuration settings"""
        box = layout.box()
        box.label(text="Agent Configuration", icon='COMMUNITY')

        col = box.column()
        col.prop(self, "default_agent_type")

        row = col.row()
        row.prop(self, "agent_max_tokens")
        row.prop(self, "agent_temperature")

        col.prop(self, "agent_top_p")

    def _draw_cost_management(self, layout):
        """Draw cost management settings"""
        box = layout.box()
        box.label(text="Cost Management", icon='FUND')

        col = box.column()
        col.prop(self, "enable_cost_tracking")

        if self.enable_cost_tracking:
            row = col.row()
            row.prop(self, "monthly_cost_limit")
            row.prop(self, "cost_alert_threshold")

# Settings-related operators
class BLENDPRO_OT_InitializeAI(bpy.types.Operator):
    """Initialize BlendPro AI system"""
    bl_idname = "blendpro.initialize_ai"
    bl_label = "Initialize AI"
    bl_options = {'REGISTER'}

    def execute(self, context):
        try:
            from ..utils.initialization import initialize_blendpro

            result = initialize_blendpro()

            if result["success"]:
                self.report({'INFO'}, f"✓ {result['message']}")
                if "api_model" in result:
                    self.report({'INFO'}, f"Using model: {result['api_model']}")
            else:
                self.report({'ERROR'}, f"✗ {result['error']}")
                return {'CANCELLED'}

        except Exception as e:
            self.report({'ERROR'}, f"Initialization failed: {str(e)}")
            return {'CANCELLED'}

        return {'FINISHED'}

class BLENDPRO_OT_TestAPIConnection(bpy.types.Operator):
    """Test API connection"""
    bl_idname = "blendpro.test_api_connection"
    bl_label = "Test API Connection"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            # Get addon preferences
            addon_prefs = context.preferences.addons[__package__.split('.')[0]].preferences

            # Update settings with current preferences
            from ..config.settings import update_settings
            update_settings(
                api_key=addon_prefs.api_key,
                base_url=addon_prefs.custom_api_url,
                temperature=addon_prefs.temperature,
                max_tokens=addon_prefs.max_tokens,
                vision_api_key=addon_prefs.vision_api_key or addon_prefs.api_key,
                vision_base_url=addon_prefs.vision_api_url or addon_prefs.custom_api_url
            )

            # Get API client
            api_client = get_api_client()

            # Test main API
            main_result = api_client.test_connection()

            if main_result["success"]:
                self.report({'INFO'}, "Main API connection successful")
            else:
                self.report({'ERROR'}, f"Main API failed: {main_result.get('error', 'Unknown error')}")
                return {'CANCELLED'}

        except Exception as e:
            self.report({'ERROR'}, f"Test failed: {str(e)}")
            return {'CANCELLED'}
        
        # Test vision API if different
        settings = get_settings()
        vision_config = settings.get_vision_api_config()
        main_config = settings.get_api_config()
        
        if vision_config["api_key"] != main_config["api_key"] or vision_config["base_url"] != main_config["base_url"]:
            vision_result = api_client.test_connection(use_vision=True)
            
            if vision_result["success"]:
                self.report({'INFO'}, "Vision API connection also successful")
            else:
                self.report({'WARNING'}, f"Vision API failed: {vision_result.get('error', 'Unknown error')}")
        
        return {'FINISHED'}

class BLENDPRO_OT_ClearCache(bpy.types.Operator):
    """Clear API cache"""
    bl_idname = "blendpro.clear_cache"
    bl_label = "Clear Cache"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        api_client = get_api_client()
        api_client.clear_cache()
        
        # Clear other caches
        from ..vision.scene_analyzer import get_scene_analyzer
        from ..workflow.scene_monitor import get_scene_health_monitor
        
        get_scene_analyzer().clear_cache()
        
        self.report({'INFO'}, "All caches cleared")
        return {'FINISHED'}

class BLENDPRO_OT_ResetSettings(bpy.types.Operator):
    """Reset settings to defaults"""
    bl_idname = "blendpro.reset_settings"
    bl_label = "Reset Settings"
    bl_options = {'REGISTER'}
    
    def invoke(self, context, event):
        return context.window_manager.invoke_confirm(self, event)
    
    def execute(self, context):
        # Reset addon preferences to defaults
        addon_prefs = context.preferences.addons[__package__.split('.')[0]].preferences
        
        # Reset to default values (this is a simplified approach)
        addon_prefs.temperature = 0.7
        addon_prefs.max_tokens = 1500
        addon_prefs.monitoring_interval = 2.0
        addon_prefs.max_concurrent_requests = 3
        addon_prefs.max_suggestions = 5
        addon_prefs.backup_interval = 300
        addon_prefs.max_backups = 10
        addon_prefs.analysis_cooldown = 1.0
        
        # Reset feature toggles
        addon_prefs.enable_vision_context = True
        addon_prefs.enable_multi_step_planning = True
        addon_prefs.enable_proactive_suggestions = True
        addon_prefs.enable_scene_monitoring = True
        addon_prefs.enable_auto_backup = True
        addon_prefs.enable_caching = True
        
        # Set dynamic defaults for models
        set_dynamic_model_defaults(addon_prefs)

        self.report({'INFO'}, "Settings reset to defaults")
        return {'FINISHED'}


class BLENDPRO_OT_SetSettingsTab(bpy.types.Operator):
    """Set active settings tab"""
    bl_idname = "blendpro.set_settings_tab"
    bl_label = "Set Settings Tab"
    bl_options = {'REGISTER'}

    tab = bpy.props.StringProperty(
        name="Tab",
        description="Settings tab to activate",
        default=""
    )

    def execute(self, context):
        context.scene.blendpro_settings_tab = self.tab
        return {'FINISHED'}


class BLENDPRO_OT_AddCustomPreset(bpy.types.Operator):
    """Add a new custom provider preset"""
    bl_idname = "blendpro.add_custom_preset"
    bl_label = "Add Custom Preset"
    bl_options = {'REGISTER', 'UNDO'}

    # Preset properties
    preset_name = bpy.props.StringProperty(
        name="Preset Name",
        description="Unique name for the preset",
        default=""
    )

    display_name = bpy.props.StringProperty(
        name="Display Name",
        description="Human-readable name for the preset",
        default=""
    )

    api_endpoint = bpy.props.StringProperty(
        name="API Endpoint",
        description="OpenAI-compatible API endpoint URL",
        default="http://localhost:4000/v1"
    )

    api_key = bpy.props.StringProperty(
        name="API Key",
        description="API key for authentication",
        default="",
        subtype='PASSWORD'
    )

    supported_models = bpy.props.StringProperty(
        name="Supported Models",
        description="Comma-separated list of supported models",
        default=""
    )

    description = bpy.props.StringProperty(
        name="Description",
        description="Description of the preset",
        default=""
    )

    def invoke(self, context, event):
        return context.window_manager.invoke_props_dialog(self, width=500)

    def draw(self, context):
        layout = self.layout

        col = layout.column()
        col.prop(self, "preset_name")
        col.prop(self, "display_name")
        col.prop(self, "api_endpoint")
        col.prop(self, "api_key")
        col.prop(self, "supported_models")
        col.prop(self, "description")

    def execute(self, context):
        from ..utils.provider_manager import get_provider_manager
        from ..config.providers import ProviderPreset

        if not self.preset_name or not self.display_name or not self.api_endpoint:
            self.report({'ERROR'}, "Preset name, display name, and API endpoint are required")
            return {'CANCELLED'}

        # Parse supported models
        models = [model.strip() for model in self.supported_models.split(',') if model.strip()]
        if not models:
            self.report({'ERROR'}, "At least one supported model is required")
            return {'CANCELLED'}

        # Create preset
        preset = ProviderPreset(
            name=self.preset_name,
            display_name=self.display_name,
            api_endpoint=self.api_endpoint,
            api_key=self.api_key,
            supported_models=models,
            description=self.description,
            is_custom=True
        )

        # Add to provider manager
        provider_manager = get_provider_manager()
        success, errors = provider_manager.create_custom_preset(preset)

        if success:
            self.report({'INFO'}, f"Custom preset '{self.preset_name}' created successfully")
            return {'FINISHED'}
        else:
            self.report({'ERROR'}, f"Failed to create preset: {', '.join(errors)}")
            return {'CANCELLED'}


class BLENDPRO_OT_EditCustomPreset(bpy.types.Operator):
    """Edit an existing custom provider preset"""
    bl_idname = "blendpro.edit_custom_preset"
    bl_label = "Edit Custom Preset"
    bl_options = {'REGISTER', 'UNDO'}

    preset_name = bpy.props.StringProperty()

    def execute(self, context):
        # For now, just show info - full edit dialog would be more complex
        self.report({'INFO'}, f"Edit functionality for '{self.preset_name}' coming soon")
        return {'FINISHED'}


class BLENDPRO_OT_DeleteCustomPreset(bpy.types.Operator):
    """Delete a custom provider preset"""
    bl_idname = "blendpro.delete_custom_preset"
    bl_label = "Delete Custom Preset"
    bl_options = {'REGISTER', 'UNDO'}

    preset_name: bpy.props.StringProperty()

    def invoke(self, context, event):
        return context.window_manager.invoke_confirm(self, event)

    def execute(self, context):
        from ..utils.provider_manager import get_provider_manager

        provider_manager = get_provider_manager()
        success, errors = provider_manager.delete_custom_preset(self.preset_name)

        if success:
            self.report({'INFO'}, f"Custom preset '{self.preset_name}' deleted successfully")
            return {'FINISHED'}
        else:
            self.report({'ERROR'}, f"Failed to delete preset: {', '.join(errors)}")
            return {'CANCELLED'}


def set_dynamic_model_defaults(preferences):
    """Set dynamic default values for model properties"""
    try:
        from ..config.models import get_default_model_for_task, get_vision_model

        # Set default models if not already set
        if not preferences.custom_model:
            preferences.custom_model = get_default_model_for_task("general")

        if not preferences.vision_model:
            preferences.vision_model = get_vision_model()

    except Exception as e:
        print(f"Warning: Could not set dynamic model defaults: {e}")
        # Fallback to hardcoded defaults
        if not preferences.custom_model:
            preferences.custom_model = "gpt-4o-mini"
        if not preferences.vision_model:
            preferences.vision_model = "gpt-4o-mini"

def register():
    """Register Blender classes"""
    bpy.utils.register_class(BLENDPROAddonPreferences)
    bpy.utils.register_class(BLENDPRO_OT_InitializeAI)
    bpy.utils.register_class(BLENDPRO_OT_TestAPIConnection)
    bpy.utils.register_class(BLENDPRO_OT_ClearCache)
    bpy.utils.register_class(BLENDPRO_OT_ResetSettings)
    bpy.utils.register_class(BLENDPRO_OT_SetSettingsTab)
    bpy.utils.register_class(BLENDPRO_OT_AddCustomPreset)
    bpy.utils.register_class(BLENDPRO_OT_EditCustomPreset)
    bpy.utils.register_class(BLENDPRO_OT_DeleteCustomPreset)

    # Register scene property for tab selection
    bpy.types.Scene.blendpro_settings_tab = bpy.props.StringProperty(
        name="Settings Tab",
        description="Active settings tab",
        default="BASIC"
    )

    # Set dynamic defaults for existing preferences
    try:
        addon_prefs = bpy.context.preferences.addons[__package__.split('.')[0]].preferences
        set_dynamic_model_defaults(addon_prefs)
    except:
        pass  # Ignore errors during registration

def unregister():
    """Unregister Blender classes"""
    # Remove scene property
    if hasattr(bpy.types.Scene, 'blendpro_settings_tab'):
        del bpy.types.Scene.blendpro_settings_tab

    bpy.utils.unregister_class(BLENDPRO_OT_DeleteCustomPreset)
    bpy.utils.unregister_class(BLENDPRO_OT_EditCustomPreset)
    bpy.utils.unregister_class(BLENDPRO_OT_AddCustomPreset)
    bpy.utils.unregister_class(BLENDPRO_OT_SetSettingsTab)
    bpy.utils.unregister_class(BLENDPRO_OT_ResetSettings)
    bpy.utils.unregister_class(BLENDPRO_OT_ClearCache)
    bpy.utils.unregister_class(BLENDPRO_OT_TestAPIConnection)
    bpy.utils.unregister_class(BLENDPRO_OT_InitializeAI)
    bpy.utils.unregister_class(BLENDPROAddonPreferences)
