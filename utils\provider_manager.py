"""
Provider Manager for BlendPro v2.1.0
Manages AI provider preset registration, validation, and operations with custom preset support
"""

import json
import os
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

from ..config.providers import ProviderPreset, get_default_provider_presets, validate_provider_preset_config
from ..config.agent_configs import AgentConfiguration, AgentType, get_default_agent_configurations
from ..utils.logger import get_logger

class ProviderManager:
    """Manages AI provider presets and their configurations with custom preset support"""

    def __init__(self):
        self.logger = get_logger("BlendPro.ProviderManager")
        self._provider_presets: Dict[str, ProviderPreset] = {}
        self._agent_configs: Dict[AgentType, AgentConfiguration] = {}
        self._config_file = self._get_config_file_path()
        self._custom_presets_file = self._get_custom_presets_file_path()

        # Load configurations
        self._load_configurations()
    
    def _get_config_file_path(self) -> str:
        """Get path to provider configuration file"""
        # Store in Blender's user config directory
        import bpy
        config_dir = bpy.utils.user_resource('CONFIG', path="blendpro")
        if not os.path.exists(config_dir):
            os.makedirs(config_dir, exist_ok=True)
        return os.path.join(config_dir, "provider_config.json")

    def _get_custom_presets_file_path(self) -> str:
        """Get path to custom provider presets file"""
        import bpy
        config_dir = bpy.utils.user_resource('CONFIG', path="blendpro")
        if not os.path.exists(config_dir):
            os.makedirs(config_dir, exist_ok=True)
        return os.path.join(config_dir, "custom_provider_presets.json")
    
    def _load_configurations(self):
        """Load provider and agent configurations from file with robust error handling"""
        try:
            if os.path.exists(self._config_file):
                with open(self._config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # Validate that data is a dictionary
                if not isinstance(data, dict):
                    self.logger.warning(f"Configuration file contains invalid data type. Expected dict, got {type(data)}. Using defaults.")
                    self._load_default_configurations()
                    return

                # Load provider presets
                presets_data = data.get("provider_presets", {})
                if isinstance(presets_data, dict):
                    for name, preset_data in presets_data.items():
                        try:
                            self._provider_presets[name] = ProviderPreset.from_dict(preset_data)
                        except Exception as e:
                            self.logger.error(f"Failed to load provider preset {name}: {e}")
                else:
                    self.logger.warning("Invalid provider presets data format in config file")

                # Load agent configurations
                agents_data = data.get("agent_configurations", {})
                if isinstance(agents_data, dict):
                    for agent_type_str, config_data in agents_data.items():
                        try:
                            agent_type = AgentType(agent_type_str)
                            self._agent_configs[agent_type] = AgentConfiguration.from_dict(config_data)
                        except Exception as e:
                            self.logger.error(f"Failed to load agent config {agent_type_str}: {e}")
                else:
                    self.logger.warning("Invalid agent configurations data format in config file")

            # Load custom presets
            self._load_custom_presets()

            # Ensure we have default configurations
            self._ensure_default_configurations()

        except json.JSONDecodeError as e:
            self.logger.warning(f"Configuration file is corrupted (JSON decode error: {e}). Using default configurations.")
            self._load_default_configurations()
        except Exception as e:
            self.logger.error(f"Failed to load configurations: {e}")
            self._load_default_configurations()
    
    def _load_custom_presets(self):
        """Load custom provider presets from file"""
        try:
            if os.path.exists(self._custom_presets_file):
                with open(self._custom_presets_file, 'r', encoding='utf-8') as f:
                    custom_presets_data = json.load(f)

                if isinstance(custom_presets_data, dict):
                    for name, preset_data in custom_presets_data.items():
                        try:
                            preset = ProviderPreset.from_dict(preset_data)
                            preset.is_custom = True  # Mark as custom preset
                            self._provider_presets[name] = preset
                        except Exception as e:
                            self.logger.error(f"Failed to load custom preset {name}: {e}")
                else:
                    self.logger.warning("Invalid custom presets data format")
        except Exception as e:
            self.logger.error(f"Failed to load custom presets: {e}")

    def _ensure_default_configurations(self):
        """Ensure default provider presets and agent configurations are available"""
        # Add missing default provider presets
        default_presets = get_default_provider_presets()
        for name, preset in default_presets.items():
            if name not in self._provider_presets:
                self._provider_presets[name] = preset

        # Add missing default agent configurations
        default_agent_configs = get_default_agent_configurations()
        for agent_type, config in default_agent_configs.items():
            if agent_type not in self._agent_configs:
                self._agent_configs[agent_type] = config

    def _load_default_configurations(self):
        """Load default configurations as fallback"""
        self.logger.info("Loading default configurations")
        self._provider_presets = get_default_provider_presets()
        self._agent_configs = get_default_agent_configurations()
    
    def _save_configurations(self):
        """Save current configurations to file"""
        try:
            # Save main configuration (non-custom presets and agent configs)
            data = {
                "provider_presets": {
                    name: preset.to_dict()
                    for name, preset in self._provider_presets.items()
                    if not preset.is_custom
                },
                "agent_configurations": {
                    agent_type.value: config.to_dict()
                    for agent_type, config in self._agent_configs.items()
                },
                "last_updated": datetime.now().isoformat()
            }

            with open(self._config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            # Save custom presets separately
            self._save_custom_presets()

            self.logger.debug("Configurations saved successfully")

        except Exception as e:
            self.logger.error(f"Failed to save configurations: {e}")

    def _save_custom_presets(self):
        """Save custom provider presets to separate file"""
        try:
            custom_presets = {
                name: preset.to_dict()
                for name, preset in self._provider_presets.items()
                if preset.is_custom
            }

            with open(self._custom_presets_file, 'w', encoding='utf-8') as f:
                json.dump(custom_presets, f, indent=2, ensure_ascii=False)

        except Exception as e:
            self.logger.error(f"Failed to save custom presets: {e}")
    
    # Provider Management Methods
    
    def create_custom_preset(self, preset: ProviderPreset) -> Tuple[bool, List[str]]:
        """Create a new custom provider preset"""
        # Validate preset configuration
        errors = validate_provider_preset_config(preset)
        if errors:
            return False, errors

        # Check if preset already exists
        if preset.name in self._provider_presets:
            return False, [f"Provider preset '{preset.name}' already exists"]

        # Mark as custom and register preset
        preset.is_custom = True
        self._provider_presets[preset.name] = preset
        self._save_configurations()

        self.logger.info(f"Custom provider preset '{preset.name}' created successfully")
        return True, []

    def update_custom_preset(self, preset: ProviderPreset) -> Tuple[bool, List[str]]:
        """Update existing custom provider preset"""
        # Validate preset configuration
        errors = validate_provider_preset_config(preset)
        if errors:
            return False, errors

        # Check if preset exists and is custom
        existing_preset = self._provider_presets.get(preset.name)
        if not existing_preset:
            return False, [f"Provider preset '{preset.name}' not found"]

        if not existing_preset.is_custom:
            return False, [f"Cannot update built-in preset '{preset.name}'. Only custom presets can be modified."]

        # Update preset
        preset.is_custom = True
        self._provider_presets[preset.name] = preset
        self._save_configurations()

        self.logger.info(f"Custom provider preset '{preset.name}' updated successfully")
        return True, []
    
    def delete_custom_preset(self, preset_name: str) -> Tuple[bool, List[str]]:
        """Delete a custom provider preset"""
        preset = self._provider_presets.get(preset_name)
        if not preset:
            return False, [f"Provider preset '{preset_name}' not found"]

        if not preset.is_custom:
            return False, [f"Cannot delete built-in preset '{preset_name}'. Only custom presets can be deleted."]

        # Check if preset is being used by any agent
        for agent_type, config in self._agent_configs.items():
            if config.provider_preset_name == preset_name:
                return False, [f"Cannot delete preset '{preset_name}' - used by agent {agent_type.value}"]

        del self._provider_presets[preset_name]
        self._save_configurations()

        self.logger.info(f"Custom provider preset '{preset_name}' deleted successfully")
        return True, []

    def get_provider_preset(self, preset_name: str) -> Optional[ProviderPreset]:
        """Get provider preset by name"""
        return self._provider_presets.get(preset_name)

    def get_all_provider_presets(self) -> Dict[str, ProviderPreset]:
        """Get all registered provider presets"""
        return self._provider_presets.copy()

    def get_active_provider_presets(self) -> Dict[str, ProviderPreset]:
        """Get only active provider presets"""
        return {
            name: preset for name, preset in self._provider_presets.items()
            if preset.is_active
        }

    def get_custom_presets(self) -> Dict[str, ProviderPreset]:
        """Get only custom provider presets"""
        return {
            name: preset for name, preset in self._provider_presets.items()
            if preset.is_custom
        }

    def validate_provider_preset(self, preset_name: str) -> Tuple[bool, List[str]]:
        """Validate provider preset configuration"""
        preset = self.get_provider_preset(preset_name)
        if not preset:
            return False, [f"Provider preset '{preset_name}' not found"]

        errors = validate_provider_preset_config(preset)
        return len(errors) == 0, errors

    def get_available_models(self, preset_name: str) -> List[str]:
        """Get available models for provider preset"""
        preset = self.get_provider_preset(preset_name)
        return preset.supported_models if preset else []

    # Backward compatibility aliases (deprecated)
    def get_provider(self, provider_name: str) -> Optional[ProviderPreset]:
        """DEPRECATED: Use get_provider_preset() instead"""
        return self.get_provider_preset(provider_name)

    def get_all_providers(self) -> Dict[str, ProviderPreset]:
        """DEPRECATED: Use get_all_provider_presets() instead"""
        return self.get_all_provider_presets()
    
    # Agent Configuration Methods
    
    def configure_agent(self, agent_type: AgentType, config: AgentConfiguration) -> Tuple[bool, List[str]]:
        """Configure agent with provider preset and model"""
        from ..config.agent_configs import validate_agent_configuration

        # Validate configuration
        errors = validate_agent_configuration(config)
        if errors:
            return False, errors

        # Check if provider preset exists and is active
        preset = self.get_provider_preset(config.provider_preset_name)
        if not preset:
            return False, [f"Provider preset '{config.provider_preset_name}' not found"]

        if not preset.is_active:
            return False, [f"Provider preset '{config.provider_preset_name}' is not active"]

        # Check if model is supported by provider preset
        if config.model_name not in preset.supported_models:
            return False, [f"Model '{config.model_name}' not supported by provider preset '{config.provider_preset_name}'"]

        # Update configuration
        self._agent_configs[agent_type] = config
        self._save_configurations()

        self.logger.info(f"Agent '{agent_type.value}' configured with provider preset '{config.provider_preset_name}' and model '{config.model_name}'")
        return True, []
    
    def get_agent_config(self, agent_type: AgentType) -> Optional[AgentConfiguration]:
        """Get configuration for specific agent"""
        return self._agent_configs.get(agent_type)
    
    def get_all_agent_configs(self) -> Dict[AgentType, AgentConfiguration]:
        """Get all agent configurations"""
        return self._agent_configs.copy()
    
    def reset_agent_config(self, agent_type: AgentType) -> bool:
        """Reset agent configuration to default"""
        from ..config.agent_configs import get_default_agent_configurations
        
        default_configs = get_default_agent_configurations()
        if agent_type in default_configs:
            self._agent_configs[agent_type] = default_configs[agent_type]
            self._save_configurations()
            self.logger.info(f"Agent '{agent_type.value}' configuration reset to default")
            return True
        return False
    
    # Utility Methods

    def get_provider_preset_status_summary(self) -> Dict[str, Any]:
        """Get summary of all provider preset statuses"""
        summary = {
            "total_presets": len(self._provider_presets),
            "active_presets": len(self.get_active_provider_presets()),
            "custom_presets": len(self.get_custom_presets()),
            "presets": {}
        }

        for name, preset in self._provider_presets.items():
            summary["presets"][name] = {
                "display_name": preset.display_name,
                "is_active": preset.is_active,
                "is_custom": preset.is_custom,
                "test_status": preset.test_status,
                "last_tested": preset.last_tested,
                "model_count": len(preset.supported_models),
                "description": preset.description
            }

        return summary

    # Backward compatibility alias
    def get_provider_status_summary(self) -> Dict[str, Any]:
        """DEPRECATED: Use get_provider_preset_status_summary() instead"""
        return self.get_provider_preset_status_summary()

# Global instance
_provider_manager = None

def get_provider_manager() -> ProviderManager:
    """Get global provider manager instance"""
    global _provider_manager
    if _provider_manager is None:
        _provider_manager = ProviderManager()
    return _provider_manager
