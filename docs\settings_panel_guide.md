# BlendPro v2.1.0 - Geliştirilmiş Ayarlar Paneli Rehberi

## Genel Bakış

BlendPro v2.1.0 ile birlikte ayarlar paneli tamamen yeniden tasarlandı ve daha kullanıcı dostu bir deneyim sunuyor. Yeni tabbed interface (sekmeli arayüz) ile ayarlarınızı daha kolay organize edebilir ve sistem durumunuzu anlık olarak takip edebilirsiniz.

## Yeni Özellikler

### 🎯 Tabbed Interface (Sekmeli Arayüz)
Ayarlar artık 5 ana kategoriye ayrılmış:

1. **Basic (Temel)** - API yapılandırması ve temel AI davranışları
2. **Agents (Ajanlar)** - Multi-agent sistem ve ajan yapılandırması
3. **Vision (Görü)** - Görü analizi ve gelişmiş görü özellikleri
4. **Performance (Performans)** - Sistem performansı ve izleme
5. **Advanced (Gelişmiş)** - Gelişmiş özellikler ve sistem durumu

### 📊 Sistem Sağlık Göstergesi
Ayarlar panelinin üst kısmında sistem sağlığınızı gösteren bir gösterge bulunur:
- **Excellent (Mükemmel)** (80-100%): Tüm önemli özellikler aktif
- **Good (İyi)** (60-79%): Çoğu özellik aktif
- **Fair (Orta)** (40-59%): Bazı özellikler eksik
- **Poor (Zayıf)** (0-39%): Önemli yapılandırmalar eksik

### 🎨 Geliştirilmiş Görsel Tasarım
- **Grid Layout**: İki sütunlu düzen ile daha iyi alan kullanımı
- **Status Indicators**: Her bölümde durum göstergeleri
- **Visual Feedback**: Ayarların etkisini gösteren görsel ipuçları
- **Organized Sections**: İlgili ayarlar gruplandırılmış

## Sekme Detayları

### 🔧 Basic (Temel) Sekmesi

**API Configuration (API Yapılandırması)**
- Main API ve Vision API ayarları yan yana
- Bağlantı durumu göstergeleri
- Test ve başlatma butonları

**AI Behavior (AI Davranışı)**
- Temperature ayarı ile görsel göstergeler:
  - Conservative (Muhafazakar) < 0.3
  - Balanced (Dengeli) 0.3-0.8
  - Creative (Yaratıcı) > 0.8
- Token limiti ile yanıt uzunluğu göstergeleri

**Essential Features (Temel Özellikler)**
- En önemli özelliklerin hızlı erişimi
- Vision Context, Multi-Step Planning, vb.

### 👥 Agents (Ajanlar) Sekmesi

**Multi-Agent System**
- Çoklu ajan sistemi ayarları
- Ajan güven eşiği
- Paralel model limitleri

**Agent Configuration**
- Varsayılan ajan tipi
- Ajan-specific token ve temperature ayarları

**Provider Management**
- Provider failover ayarları
- Timeout yapılandırması

### 👁️ Vision (Görü) Sekmesi

**Enhanced Vision Features**
- Depth Analysis (Derinlik Analizi)
- Spatial Reasoning (Mekansal Akıl Yürütme)
- Temporal Tracking (Zamansal Takip)
- Vision Analysis Quality ayarları

### ⚡ Performance (Performans) Sekmesi

**System Monitoring**
- Provider, Agent, Vision, Memory izleme
- Aktif monitor sayısı göstergesi

**Scene Analysis**
- Monitoring interval ve cooldown ayarları

**Request Limits & Backup**
- Eşzamanlı istek limitleri
- Backup sistem ayarları

**Cost Management**
- Maliyet takibi
- Aylık limit ve uyarı eşikleri

### 🔬 Advanced (Gelişmiş) Sekmesi

**Feature Configuration**
- Tüm özellik toggle'ları kategorize edilmiş
- AI Features vs System Features ayrımı
- Aktif özellik sayısı göstergesi

**System Status**
- Kapsamlı sistem durumu bilgisi
- API bağlantı durumları
- Cache ve monitoring durumu
- Sistem eylemleri (Clear Cache, Test APIs)

## Kullanım İpuçları

### 🚀 İlk Kurulum
1. **Basic** sekmesinde API anahtarlarınızı girin
2. **Test Connection** ile bağlantıyı doğrulayın
3. **Initialize AI** ile sistemi başlatın
4. Sistem sağlık göstergesinin yeşil olmasını bekleyin

### 🎯 Optimizasyon
1. **Performance** sekmesinde monitoring'i etkinleştirin
2. **Vision** sekmesinde ihtiyacınıza göre kalite ayarını seçin
3. **Advanced** sekmesinde gereksiz özellikleri kapatın

### 🔧 Sorun Giderme
1. **Advanced > System Status** bölümünde hata kontrolü yapın
2. **Clear Cache** ile önbelleği temizleyin
3. **Reset Settings** ile varsayılan ayarlara dönün

## Stil Rehberi

Yeni ayarlar paneli `ui/style_guide.py` dosyasında tanımlanan stil standartlarını kullanır:

- **Consistent Colors**: Durum renkleri (yeşil=başarılı, sarı=uyarı, kırmızı=hata)
- **Standardized Icons**: Her özellik için tutarlı ikonlar
- **Responsive Layout**: Grid sistemi ile esnek düzen
- **Visual Hierarchy**: Önem sırasına göre görsel hiyerarşi

## Geliştiriciler İçin

### Yeni Özellik Ekleme
1. `BLENDPROAddonPreferences` sınıfına property ekleyin
2. İlgili `_draw_*` metodunda UI ekleyin
3. `style_guide.py`'dan stil sabitlerini kullanın

### Stil Tutarlılığı
```python
# Örnek kullanım
header = BlendProLayout.create_header(layout, "Section Title", BlendProIcons.FEATURE)
box = BlendProLayout.create_section_box(layout, "Section", BlendProIcons.FEATURE)
grid = BlendProLayout.create_two_column_grid(layout)
```

## Sürüm Notları

### v2.1.0 Değişiklikleri
- ✅ Tabbed interface eklendi
- ✅ Sistem sağlık göstergesi eklendi
- ✅ Grid layout ile iki sütunlu düzen
- ✅ Görsel durum göstergeleri
- ✅ Stil rehberi sistemi
- ✅ Geliştirilmiş hata ayıklama araçları
- ✅ Responsive tasarım

### Gelecek Sürümler
- 🔄 Dark/Light tema desteği
- 🔄 Özelleştirilebilir tab düzeni
- 🔄 Gelişmiş performans metrikleri
- 🔄 Export/Import ayarları

## Destek

Sorunlar için:
1. **Advanced > System Status** kontrol edin
2. GitHub Issues'da rapor edin
3. Discord kanalımıza katılın

---

**Not**: Bu rehber BlendPro v2.1.0 için hazırlanmıştır. Önceki sürümlerle uyumluluk garantisi yoktur.
