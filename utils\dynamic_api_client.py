"""
Dynamic API Client for BlendPro v2.1.0
Unified OpenAI-compatible API client with dynamic provider preset switching
"""

import time
import threading
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime

from ..config.providers import ProviderPreset
from ..config.agent_configs import AgentType
from ..utils.provider_manager import get_provider_manager
from ..utils.logger import get_logger
from ..utils.api_client import APIResponse, APIError

# Import OpenAI client (only dependency needed)
try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

@dataclass
class ProviderRequest:
    """Request specific to a provider"""
    provider_name: str
    model_name: str
    messages: List[Dict[str, Any]]
    temperature: float = 0.7
    max_tokens: int = 1500
    top_p: float = 1.0
    timeout: float = 30.0
    use_vision: bool = False
    stream: bool = False

@dataclass
class ProviderResponse:
    """Response from a provider"""
    content: str
    model: str
    provider_name: str
    usage: Dict[str, int]
    finish_reason: str
    response_time: float
    cost_estimate: float = 0.0
    error: Optional[str] = None

class DynamicAPIClient:
    """Unified OpenAI-compatible API client with dynamic provider preset switching"""

    def __init__(self):
        self.logger = get_logger("BlendPro.DynamicAPI")
        self.provider_manager = get_provider_manager()
        self._clients: Dict[str, OpenAI] = {}
        self._rate_limiters: Dict[str, threading.Semaphore] = {}
        self._request_counts: Dict[str, int] = {}
        self._last_reset: Dict[str, float] = {}

        # Initialize rate limiters for all provider presets
        self._initialize_rate_limiters()

    def _initialize_rate_limiters(self):
        """Initialize rate limiters for all provider presets"""
        for preset_name, preset in self.provider_manager.get_all_provider_presets().items():
            self._rate_limiters[preset_name] = threading.Semaphore(
                preset.rate_limits.concurrent_requests
            )
            self._request_counts[preset_name] = 0
            self._last_reset[preset_name] = time.time()
    
    def _get_client(self, preset: ProviderPreset) -> OpenAI:
        """Get or create OpenAI-compatible client for provider preset"""
        if preset.name in self._clients:
            return self._clients[preset.name]

        if not OPENAI_AVAILABLE:
            raise APIError("OpenAI library not available")

        # All presets are treated as OpenAI-compatible endpoints
        client = OpenAI(
            api_key=preset.api_key,
            base_url=preset.api_endpoint,
            timeout=preset.timeout,
            max_retries=preset.max_retries
        )

        self._clients[preset.name] = client
        return client
    
    def _check_rate_limit(self, preset: ProviderPreset) -> bool:
        """Check if request is within rate limits"""
        current_time = time.time()
        preset_name = preset.name

        # Reset counter if a minute has passed
        if current_time - self._last_reset[preset_name] >= 60:
            self._request_counts[preset_name] = 0
            self._last_reset[preset_name] = current_time

        # Check if within rate limit
        if self._request_counts[preset_name] >= preset.rate_limits.requests_per_minute:
            return False

        self._request_counts[preset_name] += 1
        return True

    def _estimate_cost(self, preset: ProviderPreset, usage: Dict[str, int], use_vision: bool = False) -> float:
        """Estimate cost of request"""
        input_tokens = usage.get("prompt_tokens", 0)
        output_tokens = usage.get("completion_tokens", 0)

        cost = (
            (input_tokens / 1000) * preset.cost_config.input_cost_per_1k_tokens +
            (output_tokens / 1000) * preset.cost_config.output_cost_per_1k_tokens
        )

        if use_vision and preset.cost_config.image_cost_per_request > 0:
            cost += preset.cost_config.image_cost_per_request

        return cost
    
    def make_request_with_provider_preset(self, request: ProviderRequest) -> ProviderResponse:
        """Make request with specific provider preset"""
        preset = self.provider_manager.get_provider_preset(request.provider_name)
        if not preset:
            return ProviderResponse(
                content="",
                model=request.model_name,
                provider_name=request.provider_name,
                usage={},
                finish_reason="error",
                response_time=0.0,
                error=f"Provider preset '{request.provider_name}' not found"
            )

        if not preset.is_active:
            return ProviderResponse(
                content="",
                model=request.model_name,
                provider_name=request.provider_name,
                usage={},
                finish_reason="error",
                response_time=0.0,
                error=f"Provider preset '{request.provider_name}' is not active"
            )

        # Check rate limits
        if not self._check_rate_limit(preset):
            return ProviderResponse(
                content="",
                model=request.model_name,
                provider_name=request.provider_name,
                usage={},
                finish_reason="error",
                response_time=0.0,
                error="Rate limit exceeded"
            )

        # Use rate limiter
        with self._rate_limiters[preset.name]:
            try:
                start_time = time.time()

                # Get OpenAI-compatible client
                client = self._get_client(preset)

                # All presets use OpenAI-compatible requests
                response = self._make_openai_compatible_request(client, request)

                response_time = time.time() - start_time

                # Estimate cost
                cost_estimate = self._estimate_cost(preset, response.usage, request.use_vision)

                return ProviderResponse(
                    content=response.content,
                    model=response.model,
                    provider_name=request.provider_name,
                    usage=response.usage,
                    finish_reason=response.finish_reason,
                    response_time=response_time,
                    cost_estimate=cost_estimate
                )

            except Exception as e:
                self.logger.error(f"Request failed for provider preset {request.provider_name}: {e}")
                return ProviderResponse(
                    content="",
                    model=request.model_name,
                    provider_name=request.provider_name,
                    usage={},
                    finish_reason="error",
                    response_time=time.time() - start_time if 'start_time' in locals() else 0.0,
                    error=str(e)
                )

    # Backward compatibility alias
    def make_request_with_provider(self, request: ProviderRequest) -> ProviderResponse:
        """DEPRECATED: Use make_request_with_provider_preset() instead"""
        return self.make_request_with_provider_preset(request)

    def _make_openai_compatible_request(self, client: OpenAI, request: ProviderRequest) -> APIResponse:
        """Make request to OpenAI-compatible API (unified method for all presets)"""
        try:
            response = client.chat.completions.create(
                model=request.model_name,
                messages=request.messages,
                temperature=request.temperature,
                max_tokens=request.max_tokens,
                top_p=request.top_p,
                timeout=request.timeout,
                stream=request.stream
            )

            return APIResponse(
                content=response.choices[0].message.content,
                model=response.model,
                usage={
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                finish_reason=response.choices[0].finish_reason
            )

        except Exception as e:
            raise APIError(f"OpenAI-compatible API request failed: {str(e)}")



    def make_request_with_agent(self, agent_type: AgentType, messages: List[Dict[str, Any]],
                               use_vision: bool = False, **kwargs) -> ProviderResponse:
        """Make request using agent configuration"""
        agent_config = self.provider_manager.get_agent_config(agent_type)
        if not agent_config:
            return ProviderResponse(
                content="",
                model="",
                provider_name="",
                usage={},
                finish_reason="error",
                response_time=0.0,
                error=f"Agent configuration not found for {agent_type.value}"
            )

        request = ProviderRequest(
            provider_name=agent_config.provider_preset_name,
            model_name=agent_config.model_name,
            messages=messages,
            temperature=agent_config.temperature,
            max_tokens=agent_config.max_tokens,
            top_p=agent_config.top_p,
            use_vision=use_vision,
            **kwargs
        )

        # Try primary provider preset
        response = self.make_request_with_provider_preset(request)

        # Try fallback if primary failed and fallback is configured
        if response.error and agent_config.fallback_provider_preset:
            self.logger.warning(f"Primary provider preset failed, trying fallback: {agent_config.fallback_provider_preset}")

            fallback_request = ProviderRequest(
                provider_name=agent_config.fallback_provider_preset,
                model_name=agent_config.fallback_model or agent_config.model_name,
                messages=messages,
                temperature=agent_config.temperature,
                max_tokens=agent_config.max_tokens,
                top_p=agent_config.top_p,
                use_vision=use_vision,
                **kwargs
            )

            response = self.make_request_with_provider_preset(fallback_request)

        return response

    def test_provider_preset_connection(self, preset_name: str) -> Dict[str, Any]:
        """Test connection to a specific provider preset"""
        preset = self.provider_manager.get_provider_preset(preset_name)
        if not preset:
            return {"success": False, "error": f"Provider preset '{preset_name}' not found"}

        test_request = ProviderRequest(
            provider_name=preset_name,
            model_name=preset.default_model or (preset.supported_models[0] if preset.supported_models else ""),
            messages=[{"role": "user", "content": "Hello, please respond with 'Connection successful!'"}],
            max_tokens=50,
            timeout=10
        )

        response = self.make_request_with_provider_preset(test_request)

        if response.error:
            # Update preset test status
            preset.test_status = "failed"
            preset.last_tested = datetime.now().isoformat()
            return {"success": False, "error": response.error}

        # Update preset test status
        preset.test_status = "success"
        preset.last_tested = datetime.now().isoformat()

        return {
            "success": True,
            "content": response.content,
            "model": response.model,
            "response_time": response.response_time,
            "cost_estimate": response.cost_estimate
        }

    # Backward compatibility alias
    def test_provider_connection(self, provider_name: str) -> Dict[str, Any]:
        """DEPRECATED: Use test_provider_preset_connection() instead"""
        return self.test_provider_preset_connection(provider_name)

# Global instance
_dynamic_api_client = None

def get_dynamic_api_client() -> DynamicAPIClient:
    """Get global dynamic API client instance"""
    global _dynamic_api_client
    if _dynamic_api_client is None:
        _dynamic_api_client = DynamicAPIClient()
    return _dynamic_api_client
